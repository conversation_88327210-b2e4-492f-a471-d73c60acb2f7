#!/usr/bin/env python3
"""
Test script to demonstrate CLI functionality for campaign reporting.
"""
import subprocess
import sys
import os

def test_cli_help():
    """Test the CLI help functionality."""
    print("🧪 Testing CLI Help...")
    try:
        result = subprocess.run([
            sys.executable, "campaign_reporting_main.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        print("✅ Help command executed successfully")
        print("📄 Help output:")
        print(result.stdout)
        return True
    except Exception as e:
        print(f"❌ Error testing help: {e}")
        return False

def test_cli_validation():
    """Test CLI argument validation."""
    print("\n🧪 Testing CLI Argument Validation...")
    
    # Test missing required argument
    try:
        result = subprocess.run([
            sys.executable, "campaign_reporting_main.py"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print("✅ Correctly rejected missing required arguments")
            print("📄 Error output:")
            print(result.stderr)
        else:
            print("❌ Should have failed with missing arguments")
            return False
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        return False
    
    return True

def test_cli_with_sample_data():
    """Test CLI with sample data (dry run)."""
    print("\n🧪 Testing CLI with Sample Data...")
    
    # Create sample merchant file for testing
    sample_merchants = "123456789,987654321"
    sample_sellers = "111,222,333"
    
    try:
        # Test with direct IDs
        print("📋 Testing with direct merchant and seller IDs...")
        result = subprocess.run([
            sys.executable, "campaign_reporting_main.py",
            "--locale", "pt_BR",
            "--merchants", sample_merchants,
            "--sellers_id", sample_sellers
        ], capture_output=True, text=True, timeout=60)
        
        print(f"📊 Return code: {result.returncode}")
        print("📄 Output:")
        print(result.stdout)
        if result.stderr:
            print("⚠️ Errors/Warnings:")
            print(result.stderr)
        
        return True
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out (expected for actual API calls)")
        return True
    except Exception as e:
        print(f"❌ Error testing with sample data: {e}")
        return False

def main():
    """Run all CLI tests."""
    print("🚀 Campaign Reporting CLI Tests")
    print("=" * 50)
    
    tests = [
        test_cli_help,
        test_cli_validation,
        test_cli_with_sample_data
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
