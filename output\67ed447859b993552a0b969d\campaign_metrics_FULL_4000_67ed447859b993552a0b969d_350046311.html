
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Dashboard de Desempenho - FULL - 4000</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        <style>
            /* Optimize for single-page PDF */
            @page {
                size: auto;
                margin: 0;
            }
            body {
                /* Prevent page breaks inside elements */
                page-break-inside: avoid;
            }
            .chart-container, .insights-section, .summary-cards {
                page-break-inside: avoid;
            }
            :root {
                --primary-color: #4a90e2; /* Brighter Blue */
                --secondary-color: #50e3c2; /* Teal */
                --accent-color: #6c5ce7; /* Purple accent */
                --text-color: #2d3436;
                --text-light: #636e72;
                --text-muted: #b2bec3;
                --bg-color: #f7f9fc; /* Lighter background */
                --card-bg: #ffffff;
                --border-color: #e1e4e8;
                --shadow-color: rgba(0, 0, 0, 0.08);
                --success-bg: #d4edda;
                --success-border: #c3e6cb;
                --success-text: #155724;
                --warning-bg: #fff3cd;
                --warning-border: #ffeeba;
                --warning-text: #856404;
                --danger-bg: #f8d7da;
                --danger-border: #f5c6cb;
                --danger-text: #721c24;
                --info-bg: #d1ecf1;
                --info-border: #bee5eb;
                --info-text: #0c5460;

                /* Animation durations */
                --transition-fast: 0.2s;
                --transition-medium: 0.3s;
                --transition-slow: 0.5s;
            }

            body {
                font-family: 'Inter', sans-serif;
                margin: 0;
                padding: 25px;
                background-color: var(--bg-color);
                color: var(--text-color);
                line-height: 1.6;
                transition: background-color var(--transition-medium) ease;
            }
            .container {
                max-width: 1300px; /* Slightly wider */
                margin: 20px auto;
                background-color: var(--card-bg);
                padding: 30px; /* More padding */
                border-radius: 16px; /* Softer corners */
                box-shadow: 0 10px 30px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: all var(--transition-medium) ease;
            }
            h1, h2, h3 {
                color: var(--text-color);
                margin-bottom: 0.8em;
                font-weight: 600;
            }
            h1 { text-align: center; font-size: 2em; margin-bottom: 1.2em; }
            h2 { font-size: 1.5em; border-bottom: 1px solid var(--border-color); padding-bottom: 0.4em; margin-top: 1.5em; }
            h3 { font-size: 1.2em; color: var(--primary-color); font-weight: 500; }
            h4 { font-size: 1.1em; color: var(--text-light); font-weight: 500; margin-bottom: 0.6em; }

            .summary-cards {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* Adjusted minmax */
                gap: 20px;
                margin-bottom: 30px;
            }
            .summary-card {
                background-color: var(--card-bg);
                border-radius: 12px;
                padding: 22px;
                box-shadow: 0 8px 16px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: transform var(--transition-medium) ease, box-shadow var(--transition-medium) ease;
                border-left: 4px solid var(--primary-color);
                position: relative;
                overflow: hidden;
            }
            .summary-card::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
                z-index: 1;
            }
            .summary-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
            }
            .card-title {
                font-size: 0.9em; /* Slightly smaller */
                color: var(--text-muted);
                margin-top: 0;
                margin-bottom: 8px; /* Space between title and value */
                text-transform: uppercase;
                font-weight: 500;
            }
            .card-value {
                font-size: 1.8em; /* Adjusted size */
                font-weight: 600;
                margin-bottom: 0;
                color: var(--text-color);
                line-height: 1.2;
            }

            .chart-container {
                margin-bottom: 40px;
                padding: 25px;
                background-color: var(--card-bg);
                border-radius: 12px;
                box-shadow: 0 8px 20px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: transform var(--transition-medium) ease, box-shadow var(--transition-medium) ease;
            }
            .chart-container:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
            }
            .chart-container h2 {
                font-size: 1.4em;
                color: var(--text-color);
                margin-bottom: 20px;
                text-align: left;
                border-bottom: 2px solid #f0f4f8;
                padding-bottom: 10px;
                position: relative;
            }
            .chart-container h2::after {
                content: "";
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 80px;
                height: 2px;
                background-color: var(--primary-color);
            }

            /* Purchase Experience Styles */
            .purchase-experience-container {
                background: linear-gradient(to bottom, #ffffff, #f9fbff);
                border-radius: 12px;
                box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
                border: none;
                overflow: hidden;
            }

            .purchase-experience-container h2 {
                color: #2c3e50;
                font-size: 1.5em;
                margin-bottom: 20px;
                border-bottom: 2px solid #e7eef7;
                padding-bottom: 12px;
            }

            .info-box, .tip-box {
                display: flex;
                align-items: flex-start;
                margin-bottom: 25px;
                padding: 15px;
                border-radius: 8px;
                background-color: #f1f8ff;
                border-left: 4px solid #4a90e2;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .tip-box {
                margin-top: 25px;
                background-color: #fffbf1;
                border-left: 4px solid #f0ad4e;
            }

            .info-box:hover, .tip-box:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            }

            .info-icon, .tip-icon {
                font-size: 1.5em;
                margin-right: 15px;
                line-height: 1;
            }

            .info-content, .tip-content {
                flex: 1;
                font-size: 0.95em;
                line-height: 1.5;
            }

            .product-cards {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                margin: 25px 0;
            }

            .product-card {
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
                padding: 20px;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                border-top: 4px solid #e0e0e0;
            }

            .product-card.status-success {
                border-top-color: #28a745;
            }

            .product-card.status-warning {
                border-top-color: #ffc107;
            }

            .product-card.status-danger {
                border-top-color: #dc3545;
            }

            .product-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            .product-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #f0f0f0;
            }

            .product-name {
                font-size: 1.1em;
                font-weight: 600;
                color: #2c3e50;
                margin: 0;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 70%;
            }

            .status-badge {
                padding: 4px 8px;
                border-radius: 20px;
                font-size: 0.7em;
                font-weight: 700;
                text-transform: uppercase;
                color: white;
            }

            .status-badge.status-success {
                background-color: #28a745;
            }

            .status-badge.status-warning {
                background-color: #ffc107;
                color: #212529;
            }

            .status-badge.status-danger {
                background-color: #dc3545;
            }

            .product-rating {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 15px;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }

            .rating-label {
                font-size: 0.8em;
                color: #6c757d;
                margin-bottom: 5px;
            }

            .rating-value {
                font-size: 1.8em;
                font-weight: 700;
                color: #2c3e50;
            }

            .product-metrics h4 {
                font-size: 0.9em;
                color: #6c757d;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .metrics-list {
                list-style-type: none;
                padding: 0;
                margin: 0;
            }

            .metrics-list li {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px dashed #e0e0e0;
                font-size: 0.9em;
                position: relative;
                padding-left: 15px;
            }

            .metrics-list li:last-child {
                border-bottom: none;
            }

            .metrics-list li:before {
                content: "•";
                position: absolute;
                left: 0;
                top: 8px;
            }

            .metrics-list li.metric-success:before {
                color: #28a745;
            }

            .metrics-list li.metric-warning:before {
                color: #ffc107;
            }

            .metrics-list li.metric-danger:before {
                color: #dc3545;
            }

            .metric-value {
                font-weight: 600;
                color: #2c3e50;
            }

            @media (max-width: 768px) {
                .product-cards {
                    grid-template-columns: 1fr;
                }
            }

            .alert-container {
                padding: 20px;
                margin: 25px 0;
                border-radius: 12px;
                border: 1px solid transparent;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
            }
            .alert-container:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
            }
            .alert-info { background-color: var(--info-bg); border-color: var(--info-border); color: var(--info-text); }
            .alert-warning { background-color: var(--warning-bg); border-color: var(--warning-border); color: var(--warning-text); }
            .alert-danger { background-color: var(--danger-bg); border-color: var(--danger-border); color: var(--danger-text); }
            .alert-success { background-color: var(--success-bg); border-color: var(--success-border); color: var(--success-text); }
            .alert-container h3 { color: inherit; font-size: 1.2em; margin-top: 0; margin-bottom: 0.8em; font-weight: 600; }
            .alert-container h4 { color: inherit; font-size: 1.1em; margin-top: 1em; margin-bottom: 0.6em; font-weight: 500; }
            .alert-container p { margin-bottom: 0.7em; line-height: 1.5; }
            .alert-container p:last-child { margin-bottom: 0; }

            .insights-section {
                margin: 30px 0;
                padding: 25px;
                background-color: var(--card-bg);
                border-radius: 12px;
                box-shadow: 0 8px 20px var(--shadow-color);
                border: 1px solid var(--border-color);
                transition: transform var(--transition-medium) ease, box-shadow var(--transition-medium) ease;
            }
            .insights-section:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
            }
            .insights-section h2 {
                font-size: 1.4em;
                color: var(--text-color);
                margin-bottom: 20px;
                text-align: left;
                border-bottom: 2px solid #f0f4f8;
                padding-bottom: 10px;
                position: relative;
            }
            .insights-section h2::after {
                content: "";
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 80px;
                height: 2px;
                background-color: var(--primary-color);
            }

            .two-column {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 25px;
            }
            @media (max-width: 800px) {
                .two-column { grid-template-columns: 1fr; }
            }

            .data-box {
                background-color: var(--card-bg);
                padding: 15px;
                border-radius: 10px;
                box-shadow: 0 3px 8px var(--shadow-color);
                border: 1px solid var(--border-color);
                margin-bottom: 15px;
                transition: transform var(--transition-fast) ease, box-shadow var(--transition-fast) ease;
            }
            .data-box:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 12px rgba(0, 0, 0, 0.08);
            }
            .data-title { font-size: 0.9em; color: var(--text-muted); margin: 0 0 5px 0; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px; }
            .data-value { font-size: 1.5em; font-weight: 600; color: var(--text-color); margin: 0 0 5px 0; }
            .data-description { font-size: 0.85em; color: var(--text-light); margin: 0; }

            .metric-card {
                background-color: var(--card-bg);
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 4px var(--shadow-color);
                border: 1px solid var(--border-color);
                margin-bottom: 15px;
            }
            .metric-card h4 {
                color: var(--primary-color);
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 1.1em;
                border-bottom: 1px solid var(--border-color);
                padding-bottom: 8px;
            }

            .data-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px dashed var(--border-color); /* Subtle separator */
            }
             .data-row:last-child {
                border-bottom: none;
                margin-bottom: 0;
                padding-bottom: 0;
            }
            .data-label { color: var(--text-light); font-size: 0.9em; }
            .data-value-inline { font-weight: 600; color: var(--text-color); font-size: 0.95em; text-align: right; }

            .sub-section {
                margin-top: 15px;
                margin-left: 0; /* Remove indent */
                padding-top: 15px;
                border-top: 1px solid var(--border-color); /* Use top border instead of left */
            }
            .sub-section h4 { font-size: 1em; color: var(--text-light); border-bottom: none; margin-bottom: 10px; }

            .campaign-info {
                background: linear-gradient(135deg, #eef5fc 0%, #f5f9ff 100%);
                border-radius: 16px;
                padding: 30px;
                margin-bottom: 35px;
                border: none;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
                position: relative;
                overflow: hidden;
            }
            .campaign-info::before {
                content: "";
                position: absolute;
                top: 0;
                right: 0;
                width: 150px;
                height: 150px;
                background: radial-gradient(circle, rgba(74, 144, 226, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
                z-index: 0;
            }
            .campaign-info h2 {
                color: #004085;
                margin-top: 0;
                text-align: left;
                border-bottom: none;
                font-size: 1.6em;
                position: relative;
                z-index: 1;
            }
            .campaign-info h3 {
                color: #0056b3;
                margin-top: 1.5em;
                font-weight: 600;
                position: relative;
                z-index: 1;
            }

            .badge {
                display: inline-block;
                padding: 4px 10px; /* Adjusted padding */
                border-radius: 12px; /* Pill shape */
                font-size: 0.75em; /* Smaller font */
                font-weight: 600; /* Bolder */
                text-transform: uppercase;
                margin-right: 5px;
                line-height: 1.4;
            }
            /* Define badge colors using CSS variables for consistency */
            .badge-primary { background-color: var(--primary-color); color: white; }
            .badge-success { background-color: var(--success-text); color: white; } /* Use text color for bg */
            .badge-warning { background-color: var(--warning-text); color: white; } /* Use text color for bg */
            .badge-info { background-color: var(--info-text); color: white; } /* Use text color for bg */
            .badge-danger { background-color: var(--danger-text); color: white; }

            .progress {
                height: 10px; /* Slightly thicker */
                border-radius: 5px;
                background-color: #e9ecef;
                margin: 8px 0 15px 0; /* More margin */
                overflow: hidden; /* Ensure inner bar respects radius */
            }
            .progress-bar {
                height: 100%;
                border-radius: 5px;
                transition: width 0.5s ease-in-out; /* Smooth transition */
            }

            .lost-impression-table {
                margin-top: 25px;
                overflow-x: auto; /* Keep horizontal scroll for small screens */
                border: 1px solid var(--border-color);
                border-radius: 8px;
                box-shadow: 0 2px 4px var(--shadow-color);
            }
            .lost-impression-table table {
                width: 100%;
                border-collapse: collapse; /* Important for styling */
                border-spacing: 0; /* Remove default spacing */
            }
            .lost-impression-table th,
            .lost-impression-table td {
                padding: 12px 15px; /* Consistent padding */
                text-align: left;
                border-bottom: 1px solid var(--border-color);
            }

            /* Allow product names to wrap */
            .lost-impression-table td:first-child {
                white-space: normal; /* Allow wrapping for product names */
                word-wrap: break-word;
                max-width: 300px; /* Limit width to force wrapping */
            }

            /* Keep other cells nowrap */
            .lost-impression-table td:not(:first-child) {
                white-space: nowrap; /* Prevent wrapping in numeric cells */
            }
             .lost-impression-table th:nth-child(n+2), /* Center align numeric columns */
             .lost-impression-table td:nth-child(n+2) {
                text-align: center;
             }
            .lost-impression-table th {
                background-color: #f8f9fa; /* Light header */
                font-weight: 600;
                color: var(--text-light);
                text-transform: uppercase;
                font-size: 0.85em;
                border-bottom-width: 2px; /* Thicker bottom border for header */
            }
            .lost-impression-table tr {
                 transition: background-color 0.15s ease;
            }
            .lost-impression-table tbody tr:last-child td {
                border-bottom: none; /* Remove border from last row */
            }
            .lost-impression-table tbody tr:hover {
                background-color: #f1f5f9; /* Subtle hover effect */
            }
            /* Keep specific row colors but use variables */
            .lost-impression-table tr[style*="background-color: #d4edda"] { background-color: var(--success-bg) !important; }
            .lost-impression-table tr[style*="background-color: #d1ecf1"] { background-color: var(--info-bg) !important; }
            .lost-impression-table tr[style*="background-color: #fff3cd"] { background-color: var(--warning-bg) !important; }
            .lost-impression-table tr[style*="background-color: #f8f9fa"] { background-color: #f8f9fa !important; } /* Keep default */

            /* Utility class for summary below table */
            .table-summary {
                 margin-top: 15px;
                 padding: 15px;
                 background-color: #f8f9fa;
                 border-radius: 5px;
                 font-size: 0.9em;
                 border: 1px solid var(--border-color);
            }
            .table-summary strong { color: var(--text-color); }

            /* Chart grid layout */
            .charts-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            @media (max-width: 900px) {
                .charts-grid {
                    grid-template-columns: 1fr;
                }
            }
            .chart-item {
                background-color: #fff;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                padding: 15px;
                border: 1px solid #eaeaea;
                max-height: 900px; /* Increased by 1.5x from 600px to 900px */
                overflow-y: auto; /* Make charts scrollable vertically */
                position: relative; /* For proper scrolling */
            }

            /* Make chart legends scrollable if they have many items */
            .js-plotly-plot .legend {
                overflow-x: hidden !important;
                max-height: none !important;
                flex-wrap: wrap !important;
                overflow-y: visible !important;
            }

            /* Position product names below the chart */
            .js-plotly-plot .legend .traces {
                margin-top: 10px !important;
            }
            .chart-item h3 {
                font-size: 1.1em;
                margin-top: 0;
                margin-bottom: 10px;
                color: #333;
                text-align: center;
                position: sticky; /* Keep title visible when scrolling */
                top: 0;
                background-color: #fff;
                padding: 5px 0;
                z-index: 10;
            }
            /* Style for the scrollbar */
            .chart-item::-webkit-scrollbar {
                width: 8px;
            }
            .chart-item::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }
            .chart-item::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 4px;
            }
            .chart-item::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Dashboard de Desempenho - FULL - 4000</h1>

            <div class="campaign-info">
                <h2>Informações da Campanha</h2>
                <div class="two-column">
                    <div>
                        <div class="data-row">
                            <span class="data-label">ID da Campanha:</span>
                            <span class="data-value-inline">350046311</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">Título:</span>
                            <span class="data-value-inline">FULL - 4000</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">Estratégia:</span>
                            <span class="data-value-inline">
                                <span class="badge {'badge-success' if campaign_strategy == 'PROFITABILITY' else 'badge-warning' if campaign_strategy == 'INCREASE' else 'badge-info' if campaign_strategy == 'VISIBILITY' else 'badge-primary'}">
                                    PROFITABILITY
                                </span>
                            </span>
                        </div>

                    </div>
                    <div>
                        <div class="data-row">
                            <span class="data-label">Orçamento Diário:</span>
                            <span class="data-value-inline">R$1,800.00</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">Data de Criação:</span>
                            <span class="data-value-inline">02/10/2024 18:55</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">Última Atualização:</span>
                            <span class="data-value-inline">29/05/2025 08:39</span>
                        </div>
                    </div>
                </div>

                <!-- ACOS information moved to Insights section for consistency -->
            </div>

            <div class="summary-cards">
                <div class="summary-card">
                    <h3 class="card-title">Cliques Totais</h3>
                    <p class="card-value">11,546</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">Impressões Totais</h3>
                    <p class="card-value">7,291,905</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">Custo Total</h3>
                    <p class="card-value">R$2,049.40</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">Receita Total</h3>
                    <p class="card-value">R$28,857.33</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">CTR Média</h3>
                    <p class="card-value">0.16%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">ROAS Médio</h3>
                    <p class="card-value">14.08</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">ACOS</h3>
                    <p class="card-value">7.10%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">Tasa de Conversión (CVR) {cvr}%</h3>
                    <p class="card-value">7.22%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">Share of Voice</h3>
                    <p class="card-value">4392.00%</p>
                </div>
                <div class="summary-card">
                    <h3 class="card-title">Unidades Totais</h3>
                    <p class="card-value">1591</p>
                </div>
            </div>

            <div class="insights-section">
                <h2>Comparação de Vendas</h2>
                
        <div class='lost-impression-table'>
            <div style="margin-bottom: 15px; background-color: #f1f8ff; padding: 10px; border-radius: 5px;">
                <strong>Informação:</strong> Os dados abaixo mostram as vendas totais apenas para produtos que tiveram vendas. Produtos com zero vendas não são exibidos.
            </div>
            <table style='width: 100%; border-collapse: collapse; margin-top: 20px;'>
                <thead>
                    <tr style='background-color: #f2f2f2;'>
                        <th style='padding: 12px; text-align: left; border-bottom: 2px solid #ddd;'>Produto</th>
                        <th style='padding: 12px; text-align: center; border-bottom: 2px solid #ddd;'>MLB</th>
                        <th style='padding: 12px; text-align: center; border-bottom: 2px solid #ddd;'>Unidades Vendidas</th>
                        <th style='padding: 12px; text-align: center; border-bottom: 2px solid #ddd;'>Vendas Totais</th>
                    </tr>
                </thead>
                <tbody>
        
            <tr style='background-color: #f8f9fa;'>
                <td style='padding: 10px; text-align: left; border-bottom: 1px solid #ddd;'>Vestido Midi Canelado Casual Básico Acinturado Modelador</td>
                <td style='padding: 10px; text-align: center; border-bottom: 1px solid #ddd;'>3921999772</td>
                <td style='padding: 10px; text-align: center; border-bottom: 1px solid #ddd;'>697</td>
                <td style='padding: 10px; text-align: center; border-bottom: 1px solid #ddd;'>R$ 28.857,33</td>
            </tr>
            
                </tbody>
            </table>
        </div>
        
        <div style='margin-top: 15px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #4a90e2'>
            <strong>Resumo:</strong> Total de unidades vendidas: <strong>697</strong> | Total de vendas da campanha: <strong>R$ 28.857,33</strong>
        </div>
        
            </div>


            <div class="insights-section">
                <h2>Insights da Campanha</h2>

                <div class="alert-container {'alert-success' if 'Abaixo' in acos_status else 'alert-warning' if 'Próximo' in acos_status else 'alert-danger' if 'Acima' in acos_status else 'alert-info'}">
                    <h3>Status do ACOS</h3>
                    <p>ACOS atual: <strong>7.10%</strong> | Benchmark: 18.42%</p>
                    <p><strong>🏆 Abaixo do benchmark</strong></p>
                    <p>O ACOS (Advertising Cost of Sale) mede quanto você está gastando em publicidade em relação às vendas geradas. Um ACOS menor indica maior eficiência.</p>

                    <div style="margin-top: 15px;">
                        <h4>Alvos de ACOS</h4>
                        <div class="two-column">
                            <div>
                                <div class="data-row">
                                    <span class="data-label">ACOS Benchmark:</span>
                                    <span class="data-value-inline">18.42%</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">ACOS Target:</span>
                                    <span class="data-value-inline">25.00%</span>
                                </div>
                            </div>
                            <div>
                                
                            </div>
                        </div>

                        
                        <div class="tip-box" style="margin-top: 15px;">
                            <div class="tip-icon">💡</div>
                            <div class="tip-content">
                                <strong>Recomendação:</strong> Recomendamos ajustar o ACOS target para mais próximo do benchmark. Seu ACOS target (25.00%) está 35.7% acima do benchmark (18.42%).
                            </div>
                        </div>
                        
                    </div>
                </div>

                <div class="alert-container alert-info">
                    <h3>Métricas de Impressões e Visibilidade</h3>
                    <div class="two-column">
                        <div>
                            <div class="data-box">
                                <p class="data-title">Estatísticas de Impressão</p>
                                <div style="margin-top: 15px;">
                                    <div style="margin-bottom: 15px;">
                                        <p style="font-weight: 500; margin-bottom: 5px;">Taxa de Impressão</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">24.0%</p>
                                        <p style="font-size: 0.9em; color: #666;">dos anúncios possíveis foram exibidos</p>
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <p style="font-weight: 500; margin-bottom: 5px;">Taxa de Impressão Top</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">5.5%</p>
                                        <p style="font-size: 0.9em; color: #666;">leilões ganhos nas primeiras posições</p>
                                    </div>
                                    <div>
                                        <p style="font-weight: 500; margin-bottom: 5px;">Total de Impressões Perdidas</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">76.0%</p>
                                        <p style="font-size: 0.9em; color: #666;">oportunidades de exibição não aproveitadas</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="data-box">
                                <p class="data-title">Motivos de Perda de Impressões</p>
                                <div style="margin-top: 15px;">
                                    <div style="margin-bottom: 15px;">
                                        <p style="font-weight: 500; margin-bottom: 5px;">Orçamento Insuficiente</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">0.0%</p>
                                        <p style="font-size: 0.9em; color: #666;">(0.0% das perdas totais)</p>
                                    </div>
                                    <div>
                                        <p style="font-weight: 500; margin-bottom: 5px;">Classificação Baixa</p>
                                        <p style="font-size: 1.2em; font-weight: bold; margin: 5px 0;">76.0%</p>
                                        <p style="font-size: 0.9em; color: #666;">(100.0% das perdas totais)</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert-container alert-success">
                    <h3>Métricas Detalhadas de Vendas</h3>

                    <div class="two-column">
                        <div class="metric-card">
                            <h4>Vendas sem Publicidade (Orgânicas)</h4>
                            <div class="data-row">
                                <span class="data-label">Quantidade de Produtos:</span>
                                <span class="data-value-inline">2003 itens</span>
                            </div>
                            <div class="data-row">
                                <span class="data-label">Quantidade de Unidades:</span>
                                <span class="data-value-inline">2048 unidades</span>
                            </div>
                            <div class="data-row">
                                <span class="data-label">Valor Total:</span>
                                <span class="data-value-inline">R$80,418.00</span>
                            </div>
                        </div>

                        <div class="metric-card">
                            <h4>Vendas com Publicidade</h4>
                            <div class="data-row">
                                <span class="data-label">Total de Vendas via Anúncios:</span>
                                <span class="data-value-inline">1569 itens</span>
                            </div>

                            <div class="sub-section">
                                <h4>Vendas Diretas</h4>
                                <div class="data-row">
                                    <span class="data-label">Quantidade de Produtos:</span>
                                    <span class="data-value-inline">1311 itens</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">Quantidade de Unidades:</span>
                                    <span class="data-value-inline">1330 unidades</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">Valor Total:</span>
                                    <span class="data-value-inline">R$52,938.54</span>
                                </div>
                            </div>

                            <div class="sub-section">
                                <h4>Vendas Indiretas</h4>
                                <div class="data-row">
                                    <span class="data-label">Quantidade de Produtos:</span>
                                    <span class="data-value-inline">258 itens</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">Quantidade de Unidades:</span>
                                    <span class="data-value-inline">261 unidades</span>
                                </div>
                                <div class="data-row">
                                    <span class="data-label">Valor Total:</span>
                                    <span class="data-value-inline">R$9,634.42</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            
    <div class="insights-section">
        <h2>Impressões Perdidas</h2>

        <div class="alert-container {'alert-warning' if total_lost > 30 else 'alert-success' if total_lost < 15 else 'alert-info'}">
            <h3>Motivos de Perda de Impressões</h3>

            <div class="two-column">
                <div>
                    <div class="chart-item" style="max-height: 525px; overflow: visible;">
                        <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="138c6c54-a437-4d05-ad88-0f7f0e77003d" class="plotly-graph-div" style="height:525px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("138c6c54-a437-4d05-ad88-0f7f0e77003d")) {                    Plotly.newPlot(                        "138c6c54-a437-4d05-ad88-0f7f0e77003d",                        [{"hole":0.4,"labels":["Por Or\u00e7amento Insuficiente","Por Classifica\u00e7\u00e3o Baixa"],"marker":{"colors":["#ff9999","#66b3ff"]},"textinfo":"percent+label","values":[0.0,76.0],"type":"pie"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"margin":{"l":30,"r":30,"t":80,"b":30,"pad":4},"title":{"text":"Motivos de Impress\u00f5es Perdidas"},"height":525,"showlegend":true},                        {"responsive": true}                    )                };            </script>        </div>
                    </div>
                </div>
                <div>
                    <p>Sua campanha está perdendo <strong>76.0%</strong> das impressões possíveis pelos seguintes motivos:</p>

                    <div class="data-row">
                        <span class="data-label">Por Orçamento Insuficiente:</span>
                        <span class="data-value-inline"><strong>0.0%</strong> (0.0% das perdas)</span>
                    </div>

                    <div class="data-row">
                        <span class="data-label">Por Classificação Baixa:</span>
                        <span class="data-value-inline"><strong>76.0%</strong> (100.0% das perdas)</span>
                    </div>

                    <div style="margin-top: 20px;">
                        <p>A <strong>Taxa de Impressão</strong> atual da sua campanha é de <strong>24.0%</strong>, o que significa que seus anúncios estão sendo exibidos em 24.0% das oportunidades possíveis.</p>
                    </div>

                    

                    

                    
                    <div class="alert-container alert-info" style="margin-top: 20px;">
                        <h4>Classificação dos Anúncios</h4>
                        <p>Sua campanha está perdendo <strong>76.0%</strong> das impressões possíveis devido à falta ou baixa classificação dos anúncios.</p>
                        </ul>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
            
    <div class="chart-container purchase-experience-container">
        <h2>Problemas na Experiência de Compra</h2>
        <div class="info-box">
            <div class="info-icon">⚠️</div>
            <div class="info-content">
                <strong>Atenção:</strong> Os dados abaixo mostram problemas identificados na experiência de compra dos produtos. Resolver estes problemas pode ajudar a melhorar a classificação dos anúncios e reduzir as impressões perdidas.
            </div>
        </div>

        <div class="product-cards">
    
        <div class="product-card status-danger">
            <div class="product-header">
                <h3 class="product-name">MLB3921999772</h3>
                <span class="status-badge status-Excelente">EXCELENTE</span>
            </div>
            <div class="product-rating">
                <div class="rating-label">Avaliação Geral</div>
                <div class="rating-value">100 - Boa</div>
            </div>
            
            <div class="product-problems">
                <h4>Principais Problemas</h4>
                <div class="problems-list">
            
                <div class="problem-item status-info">
                    <div class="problem-header">
                        <span class="problem-tag">PROBLEMA PRINCIPAL</span>
                        <span class="problem-quantity">63 problemas</span>
                    </div>
                    <div class="problem-title">Eram diferentes do que foi pedido</div>
                <div class="problem-detail">Não é da cor, tamanho ou modelo escolhido</div>
                    <div class="problem-remedy">
                        <strong>Recomendação:</strong> Certifique-se de que seu anúncio corresponda ao que você for enviar. Verifique se a foto, título e descrição são os corretos. Se aplicável, adicione as variações do produto: tamanho, cor, modelo.
                    </div>
                    
                </div>
                
                <div class="problem-item status-info">
                    <div class="problem-header">
                        <span class="problem-tag"></span>
                        <span class="problem-quantity">43 problemas</span>
                    </div>
                    <div class="problem-title">As medidas não correspondem às do guia de tamanhos</div>
                
                    <div class="problem-remedy">
                        <strong>Recomendação:</strong> Antes de enviar, verifique se as medidas dos seus produtos correspondem às do guia de tamanhos.
                    </div>
                    
                </div>
                
                </div>
            </div>
            
        </div>
        
        </div>

        <div class="tip-box">
            <div class="tip-icon">💡</div>
            <div class="tip-content">
                <strong>Dica:</strong> Resolver os problemas listados acima pode melhorar significativamente a classificação dos seus anúncios. Produtos com problemas na experiência de compra têm menor visibilidade nos resultados de busca, o que reduz o potencial de vendas.
            </div>
        </div>
    </div>

    <style>
        /* Purchase Experience Styles */.purchase-experience-container {
            background: linear-gradient(to bottom, #ffffff, #f9fbff);
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            border: none;
            overflow: hidden;
            margin-bottom: 30px;
            padding: 25px;
        }

        .purchase-experience-container h2 {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 20px;
            border-bottom: 2px solid #e7eef7;
            padding-bottom: 12px;
        }

        .info-box, .tip-box {
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f1f8ff;
            border-left: 4px solid #4a90e2;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .tip-box {
            margin-top: 25px;
            background-color: #fffbf1;
            border-left: 4px solid #f0ad4e;
        }

        .info-box:hover, .tip-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .info-icon, .tip-icon {
            font-size: 1.5em;
            margin-right: 15px;
            line-height: 1;
        }

        .info-content, .tip-content {
            flex: 1;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .product-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .product-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-top: 4px solid #e0e0e0;
        }

        .product-card.status-success {
            border-top-color: #28a745;
        }

        .product-card.status-warning {
            border-top-color: #ffc107;
        }

        .product-card.status-danger {
            border-top-color: #dc3545;
        }

        .product-card.status-secondary {
            border-top-color: #6c757d;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .product-name {
            font-size: 1em;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80%;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            white-space: normal;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.7em;
            font-weight: 700;
            text-transform: uppercase;
            color: white;
        }

        .status-badge.status-success {
            background-color: #28a745;
        }

        .status-badge.status-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .status-badge.status-danger {
            background-color: #dc3545;
        }

        .status-badge.status-secondary {
            background-color: #6c757d;
        }

        .product-rating {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .rating-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .rating-value {
            font-size: 1.8em;
            font-weight: 700;
            color: #2c3e50;
        }

        .product-metrics h4 {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metrics-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .metrics-list li {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dashed #e0e0e0;
            font-size: 0.9em;
            position: relative;
            padding-left: 15px;
        }

        .metrics-list li:last-child {
            border-bottom: none;
        }

        .metrics-list li:before {
            content: "•";
            position: absolute;
            left: 0;
            top: 8px;
        }

        .metrics-list li.metric-success:before {
            color: #28a745;
        }

        .metrics-list li.metric-warning:before {
            color: #ffc107;
        }

        .metrics-list li.metric-danger:before {
            color: #dc3545;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }

        /* Problem styles */
        .product-problems {
            margin-top: 20px;
            border-top: 1px solid #f0f0f0;
            padding-top: 15px;
        }

        .product-problems h4 {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .problems-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .problem-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            border-left: 3px solid #6c757d;
        }

        .problem-item.status-info {
            border-left-color: #17a2b8;
        }

        .problem-item.status-warning {
            border-left-color: #ffc107;
        }

        .problem-item.status-danger {
            border-left-color: #dc3545;
        }

        .problem-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .problem-tag {
            font-size: 0.7em;
            font-weight: 700;
            text-transform: uppercase;
            color: #6c757d;
        }

        .problem-quantity {
            font-size: 0.8em;
            font-weight: 600;
            color: #dc3545;
        }

        .problem-title {
            font-weight: 600;
            font-size: 0.9em;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .problem-detail {
            font-size: 0.85em;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .problem-remedy {
            font-size: 0.85em;
            color: #2c3e50;
            background-color: #e8f4f8;
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
        }

        /* Insufficient data message styling */
        .insufficient-data-message {
            display: flex;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #6c757d;
            margin-top: 10px;
        }

        .insufficient-data-message .info-icon {
            font-size: 1.5em;
            margin-right: 15px;
            line-height: 1;
        }

        .insufficient-data-message .info-content {
            flex: 1;
        }

        .insufficient-data-message p {
            margin: 0 0 10px 0;
            font-size: 0.9em;
            color: #495057;
        }

        .insufficient-data-message ul {
            margin: 0;
            padding-left: 20px;
            font-size: 0.85em;
            color: #6c757d;
        }

        .insufficient-data-message li {
            margin-bottom: 5px;
        }
    </style>
    
            <div class="insights-section">
                <h2>Gráficos de Desempenho</h2>

                <div class="charts-grid" style="grid-template-columns: 1fr; gap: 60px;">
                    <div class="chart-item" style="padding: 25px 25px 225px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">Vendas por Anúncios</h3>
                        <div>                            <div id="0e38a585-1416-473f-b6cf-997a6f20f140" class="plotly-graph-div" style="height:750px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("0e38a585-1416-473f-b6cf-997a6f20f140")) {                    Plotly.newPlot(                        "0e38a585-1416-473f-b6cf-997a6f20f140",                        [{"connectgaps":true,"hovertemplate":"\u003cb\u003e%{x|%d\u002f%m\u002f%Y}\u003c\u002fb\u003e\u003cbr\u003eVendas: R$%{y:.2f}\u003cextra\u003eProduto 3921999772\u003c\u002fextra\u003e","line":{"color":"#4285F4","width":2},"marker":{"color":"#4285F4","size":8},"mode":"lines+markers","name":"3921999772","x":["2025-04-29T00:00:00.*********","2025-04-30T00:00:00.*********","2025-05-01T00:00:00.*********","2025-05-02T00:00:00.*********","2025-05-03T00:00:00.*********","2025-05-04T00:00:00.*********","2025-05-05T00:00:00.*********","2025-05-06T00:00:00.*********","2025-05-07T00:00:00.*********","2025-05-08T00:00:00.*********","2025-05-09T00:00:00.*********","2025-05-10T00:00:00.*********","2025-05-11T00:00:00.*********","2025-05-12T00:00:00.*********","2025-05-13T00:00:00.*********","2025-05-14T00:00:00.*********","2025-05-15T00:00:00.*********","2025-05-16T00:00:00.*********","2025-05-17T00:00:00.*********","2025-05-18T00:00:00.*********","2025-05-19T00:00:00.*********","2025-05-20T00:00:00.*********","2025-05-21T00:00:00.*********","2025-05-22T00:00:00.*********","2025-05-23T00:00:00.*********","2025-05-24T00:00:00.*********","2025-05-25T00:00:00.*********","2025-05-26T00:00:00.*********","2025-05-27T00:00:00.*********","2025-05-28T00:00:00.*********"],"y":{"dtype":"f8","bdata":"9ihcj0J5oEB7FK5H4YGhQIXrUbgeS5BAZmZmZmY+lUC4HoXrUdCKQM3MzMzMWpBAAAAAAAArk0AUrkfheoqZQFK4HoXrzplAhetRuB6ll0CamZmZGW+gQD0K16Nw2JFAAAAAAACcg0DhehSuR\u002fGBQFyPwvUoEJFArkfhehQuj0CamZmZmReLQEjhehSuK4JAw\u002fUoXI8eg0DD9Shcj6JvQFK4HoXrqYFAKVyPwvU6hkC4HoXrUfaNQBSuR+F6OIdArkfhehROYECuR+F6FE5wQFK4HoXrqYFA9ihcj8IFc0CuR+F6FCp9QMP1KFyPToNA"},"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"xaxis":{"title":{"font":{"size":14},"text":""},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"yaxis":{"title":{"font":{"size":14},"text":"Vendas por An\u00fancios (R$)"},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"legend":{"font":{"size":12},"orientation":"h","yanchor":"top","y":-0.25,"xanchor":"center","x":0.5,"bgcolor":"rgba(255, 255, 255, 0.95)","bordercolor":"rgba(0, 0, 0, 0.1)","borderwidth":1,"itemsizing":"constant","traceorder":"normal"},"margin":{"l":50,"r":50,"t":120,"b":80,"pad":10},"plot_bgcolor":"rgba(250, 250, 250, 0.5)","paper_bgcolor":"rgba(0,0,0,0)","height":750,"hovermode":"closest","showlegend":true},                        {"responsive": true}                    )                };            </script>        </div>
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 225px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">Vendas Orgânicas</h3>
                        <div>                            <div id="e8e6227f-8111-4685-911b-7126e345b977" class="plotly-graph-div" style="height:750px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("e8e6227f-8111-4685-911b-7126e345b977")) {                    Plotly.newPlot(                        "e8e6227f-8111-4685-911b-7126e345b977",                        [{"connectgaps":true,"hovertemplate":"\u003cb\u003e%{x|%d\u002f%m\u002f%Y}\u003c\u002fb\u003e\u003cbr\u003eunidades: %{y}\u003cextra\u003eProduto 3921999772\u003c\u002fextra\u003e","line":{"color":"#4285F4","width":2},"marker":{"color":"#4285F4","size":8},"mode":"lines+markers","name":"3921999772","x":["2025-04-29T00:00:00.*********","2025-04-30T00:00:00.*********","2025-05-01T00:00:00.*********","2025-05-02T00:00:00.*********","2025-05-03T00:00:00.*********","2025-05-04T00:00:00.*********","2025-05-05T00:00:00.*********","2025-05-06T00:00:00.*********","2025-05-07T00:00:00.*********","2025-05-08T00:00:00.*********","2025-05-09T00:00:00.*********","2025-05-10T00:00:00.*********","2025-05-11T00:00:00.*********","2025-05-12T00:00:00.*********","2025-05-13T00:00:00.*********","2025-05-14T00:00:00.*********","2025-05-15T00:00:00.*********","2025-05-16T00:00:00.*********","2025-05-17T00:00:00.*********","2025-05-18T00:00:00.*********","2025-05-19T00:00:00.*********","2025-05-20T00:00:00.*********","2025-05-21T00:00:00.*********","2025-05-22T00:00:00.*********","2025-05-23T00:00:00.*********","2025-05-24T00:00:00.*********","2025-05-25T00:00:00.*********","2025-05-26T00:00:00.*********","2025-05-27T00:00:00.*********","2025-05-28T00:00:00.*********"],"y":{"dtype":"i1","bdata":"Ki0ZFxkTHBsmIBQPFAsdHRsiExwTHRYOFAkJEw8K"},"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"xaxis":{"title":{"font":{"size":14},"text":""},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"yaxis":{"title":{"font":{"size":14},"text":"Unidades Vendidas"},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"legend":{"font":{"size":12},"orientation":"h","yanchor":"top","y":-0.25,"xanchor":"center","x":0.5,"bgcolor":"rgba(255, 255, 255, 0.95)","bordercolor":"rgba(0, 0, 0, 0.1)","borderwidth":1,"itemsizing":"constant","traceorder":"normal"},"margin":{"l":50,"r":50,"t":120,"b":80,"pad":10},"plot_bgcolor":"rgba(250, 250, 250, 0.5)","paper_bgcolor":"rgba(0,0,0,0)","height":750,"hovermode":"closest","showlegend":true},                        {"responsive": true}                    )                };            </script>        </div>
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 225px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">ACOS ao Longo do Tempo</h3>
                        <div>                            <div id="4aa79c0f-7980-4492-8d7c-61db58d86828" class="plotly-graph-div" style="height:750px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("4aa79c0f-7980-4492-8d7c-61db58d86828")) {                    Plotly.newPlot(                        "4aa79c0f-7980-4492-8d7c-61db58d86828",                        [{"connectgaps":true,"hovertemplate":"\u003cb\u003e%{x|%d\u002f%m\u002f%Y}\u003c\u002fb\u003e\u003cbr\u003eACOS: %{y:.2f}%\u003cextra\u003eProduto 3921999772\u003c\u002fextra\u003e","line":{"color":"#4285F4","width":2},"marker":{"color":"#4285F4","size":8},"mode":"lines+markers","name":"3921999772","x":["2025-04-29T00:00:00.*********","2025-04-30T00:00:00.*********","2025-05-01T00:00:00.*********","2025-05-02T00:00:00.*********","2025-05-03T00:00:00.*********","2025-05-04T00:00:00.*********","2025-05-05T00:00:00.*********","2025-05-06T00:00:00.*********","2025-05-07T00:00:00.*********","2025-05-08T00:00:00.*********","2025-05-09T00:00:00.*********","2025-05-10T00:00:00.*********","2025-05-11T00:00:00.*********","2025-05-12T00:00:00.*********","2025-05-13T00:00:00.*********","2025-05-14T00:00:00.*********","2025-05-15T00:00:00.*********","2025-05-16T00:00:00.*********","2025-05-17T00:00:00.*********","2025-05-18T00:00:00.*********","2025-05-19T00:00:00.*********","2025-05-20T00:00:00.*********","2025-05-21T00:00:00.*********","2025-05-22T00:00:00.*********","2025-05-23T00:00:00.*********","2025-05-24T00:00:00.*********","2025-05-25T00:00:00.*********","2025-05-26T00:00:00.*********","2025-05-27T00:00:00.*********","2025-05-28T00:00:00.*********"],"y":{"dtype":"f8","bdata":"FqCFocPSI0B1+PlgvUgdQLsT6lIbySNA9LKITyCVFEBW4+UjwNQXQN4EM97h7xBAUrLyfG91H0AscnXYbywXQGlpvtV\u002fkCBAnJyLFYLFIEAkfWZTeq8WQLcyVcIn0hZANIWVZcKhJUDPYLaP65Q5QITQJ6I3FxhAnrWCvBwXEUBrFLvzTKwOQBrjsba0BRhAf1Vpq4N2EkBTZtkVPGUoQIn48weThBZAUuz8NlCuGkAfx9C5YYERQEo\u002fP4W2pxRAnh4CSbaGM0AMiFcJvXghQGDjI7H6RgZAAvpBj05pHkATPVyVofUdQPIxX2N7hRdA"},"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"xaxis":{"title":{"font":{"size":14},"text":""},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"yaxis":{"title":{"font":{"size":14},"text":"ACOS (%)"},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"legend":{"font":{"size":12},"orientation":"h","yanchor":"top","y":-0.25,"xanchor":"center","x":0.5,"bgcolor":"rgba(255, 255, 255, 0.95)","bordercolor":"rgba(0, 0, 0, 0.1)","borderwidth":1,"itemsizing":"constant","traceorder":"normal"},"margin":{"l":50,"r":50,"t":120,"b":80,"pad":10},"plot_bgcolor":"rgba(250, 250, 250, 0.5)","paper_bgcolor":"rgba(0,0,0,0)","height":750,"hovermode":"closest","showlegend":true,"shapes":[{"line":{"color":"red","dash":"dash","width":2},"type":"line","x0":"2025-04-29T00:00:00","x1":"2025-05-29T00:00:00","y0":18.42,"y1":18.42}],"annotations":[{"arrowcolor":"red","arrowhead":2,"arrowsize":1,"arrowwidth":2,"ax":-80,"ay":-30,"bgcolor":"white","bordercolor":"red","borderpad":4,"borderwidth":2,"opacity":0.8,"showarrow":true,"text":"ACOS Benchmark: 18.4%","x":"2025-05-29T00:00:00","xref":"x","y":18.42,"yref":"y"}]},                        {"responsive": true}                    )                };            </script>        </div>
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 330px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">CTR (Taxa de Cliques)</h3>
                        <div>                            <div id="81071697-abb9-4776-a1bb-a58ed88fe6c9" class="plotly-graph-div" style="height:750px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("81071697-abb9-4776-a1bb-a58ed88fe6c9")) {                    Plotly.newPlot(                        "81071697-abb9-4776-a1bb-a58ed88fe6c9",                        [{"connectgaps":true,"hovertemplate":"\u003cb\u003e%{x|%d\u002f%m\u002f%Y}\u003c\u002fb\u003e\u003cbr\u003eCTR: %{y:.2f}%\u003cextra\u003eProduto 3921999772\u003c\u002fextra\u003e","line":{"color":"#4285F4","width":2},"marker":{"color":"#4285F4","size":8},"mode":"lines+markers","name":"3921999772","x":["2025-04-29T00:00:00.*********","2025-04-30T00:00:00.*********","2025-05-01T00:00:00.*********","2025-05-02T00:00:00.*********","2025-05-03T00:00:00.*********","2025-05-04T00:00:00.*********","2025-05-05T00:00:00.*********","2025-05-06T00:00:00.*********","2025-05-07T00:00:00.*********","2025-05-08T00:00:00.*********","2025-05-09T00:00:00.*********","2025-05-10T00:00:00.*********","2025-05-11T00:00:00.*********","2025-05-12T00:00:00.*********","2025-05-13T00:00:00.*********","2025-05-14T00:00:00.*********","2025-05-15T00:00:00.*********","2025-05-16T00:00:00.*********","2025-05-17T00:00:00.*********","2025-05-18T00:00:00.*********","2025-05-19T00:00:00.*********","2025-05-20T00:00:00.*********","2025-05-21T00:00:00.*********","2025-05-22T00:00:00.*********","2025-05-23T00:00:00.*********","2025-05-24T00:00:00.*********","2025-05-25T00:00:00.*********","2025-05-26T00:00:00.*********","2025-05-27T00:00:00.*********","2025-05-28T00:00:00.*********"],"y":{"dtype":"f8","bdata":"GLdaWhKhxj\u002f+xEuPBRLEPwZ09z6NhsI\u002fMPqSnHC4vz+Wg\u002foQGJ\u002fDPyDWtYezFsI\u002fHSNOz2EQwT91kOIa0f7CP1gMPyvZmcU\u002fXTmKGxmhxD9ckGQbgC7KP7wodSRt9cQ\u002fNhf\u002fIL8+xT9tFL+58qbFPzO9vQ59A8s\u002f3r0oad4+yD\u002fOHCcdbs\u002fFP4NoieLh5sU\u002fD9tGdIq6xz8mCcDkwFbGP\u002fityTw6mMY\u002fv\u002fvPCs7CxT+NerMkdIzEPwXK3XOh3sM\u002fRSgtu9AuwT8dXjk4LdjEP9WXmcK9msI\u002fQp9iciHbvj\u002f5DrlZ0B68P9yw9N+jeL0\u002f"},"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"xaxis":{"title":{"font":{"size":14},"text":""},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"yaxis":{"title":{"font":{"size":14},"text":"CTR (%)"},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"legend":{"font":{"size":12},"orientation":"h","yanchor":"top","y":-0.25,"xanchor":"center","x":0.5,"bgcolor":"rgba(255, 255, 255, 0.95)","bordercolor":"rgba(0, 0, 0, 0.1)","borderwidth":1,"itemsizing":"constant","traceorder":"normal"},"margin":{"l":50,"r":50,"t":120,"b":80,"pad":10},"plot_bgcolor":"rgba(250, 250, 250, 0.5)","paper_bgcolor":"rgba(0,0,0,0)","height":750,"hovermode":"closest","showlegend":true},                        {"responsive": true}                    )                };            </script>        </div>
                    </div>

                    <div class="chart-item" style="padding: 25px 25px 330px 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.08); border-radius: 12px; max-height: none; overflow: visible;">
                        <h3 style="font-size: 1.3em; margin-bottom: 20px; color: #4a90e2;">CPC (Custo por Clique)</h3>
                        <div>                            <div id="773bd61f-ec16-4de1-afba-1bfbd99c6633" class="plotly-graph-div" style="height:750px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("773bd61f-ec16-4de1-afba-1bfbd99c6633")) {                    Plotly.newPlot(                        "773bd61f-ec16-4de1-afba-1bfbd99c6633",                        [{"connectgaps":true,"hovertemplate":"\u003cb\u003e%{x|%d\u002f%m\u002f%Y}\u003c\u002fb\u003e\u003cbr\u003eCPC: R$%{y:.2f}\u003cextra\u003eProduto 3921999772\u003c\u002fextra\u003e","line":{"color":"#4285F4","width":2},"marker":{"color":"#4285F4","size":8},"mode":"lines+markers","name":"3921999772","x":["2025-04-29T00:00:00.*********","2025-04-30T00:00:00.*********","2025-05-01T00:00:00.*********","2025-05-02T00:00:00.*********","2025-05-03T00:00:00.*********","2025-05-04T00:00:00.*********","2025-05-05T00:00:00.*********","2025-05-06T00:00:00.*********","2025-05-07T00:00:00.*********","2025-05-08T00:00:00.*********","2025-05-09T00:00:00.*********","2025-05-10T00:00:00.*********","2025-05-11T00:00:00.*********","2025-05-12T00:00:00.*********","2025-05-13T00:00:00.*********","2025-05-14T00:00:00.*********","2025-05-15T00:00:00.*********","2025-05-16T00:00:00.*********","2025-05-17T00:00:00.*********","2025-05-18T00:00:00.*********","2025-05-19T00:00:00.*********","2025-05-20T00:00:00.*********","2025-05-21T00:00:00.*********","2025-05-22T00:00:00.*********","2025-05-23T00:00:00.*********","2025-05-24T00:00:00.*********","2025-05-25T00:00:00.*********","2025-05-26T00:00:00.*********","2025-05-27T00:00:00.*********","2025-05-28T00:00:00.*********"],"y":{"dtype":"f8","bdata":"aCcjXC2gzT+q+HFDkgvNP8P9gCQrdMc\u002fapRsxNRUxj+Nfg+HrBPDP+58PzVeusE\u002fC9ejcD0Kxz+duV46kujHP60ZzZrRrMk\u002faGwYgfO9yT8kaojMpY7KP166SQwCK8c\u002fRGR3qcrdxz8W2mA8b3DPP1kYt0\u002fdI8Q\u002fWmQ7308NwT9ewcMnKo7AP5jo72S1vME\u002fxZpLXAbZvz\u002fOx7WhYpy\u002fP6PUKRFefcA\u002f26geq43qwT+ZMcMspn7BPw6oQdt0DsI\u002flDYTGhHOwD\u002fi\u002fd5y+EvAP3j1a20eyLw\u002frsJ2ccQFwT8XOnnQnA\u002fEPyZG5YysS8M\u002f"},"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"xaxis":{"title":{"font":{"size":14},"text":""},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"yaxis":{"title":{"font":{"size":14},"text":"CPC (R$)"},"showgrid":true,"gridwidth":1,"gridcolor":"rgba(220, 220, 220, 0.5)","zeroline":false},"legend":{"font":{"size":12},"orientation":"h","yanchor":"top","y":-0.25,"xanchor":"center","x":0.5,"bgcolor":"rgba(255, 255, 255, 0.95)","bordercolor":"rgba(0, 0, 0, 0.1)","borderwidth":1,"itemsizing":"constant","traceorder":"normal"},"margin":{"l":50,"r":50,"t":120,"b":80,"pad":10},"plot_bgcolor":"rgba(250, 250, 250, 0.5)","paper_bgcolor":"rgba(0,0,0,0)","height":750,"hovermode":"closest","showlegend":true},                        {"responsive": true}                    )                };            </script>        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    