"""
Demonstration script showing the complete layered architecture functionality.
"""
import sys
import os

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(__file__))

def demo_layered_architecture():
    """Demonstrate the layered architecture functionality."""
    print("🎯 Campaign Reporting - Layered Architecture Demo")
    print("=" * 60)
    
    print("\n📋 **ARCHITECTURE OVERVIEW**")
    print("The monolithic script has been refactored into a clean 3-layer architecture:")
    print("  🔧 Data Layer      - External data access (APIs, files)")
    print("  💼 Business Layer  - Core business logic and calculations")
    print("  🎨 Presentation    - User interface and orchestration")
    print("  ⚙️  Configuration   - Centralized settings")
    
    print("\n📁 **NEW FILE ORGANIZATION**")
    print("Files are now organized by merchant in dedicated directories:")
    print("  output/{merchant_id}/")
    print("    ├── {merchant}_campaigns.csv          # Campaign data")
    print("    ├── {merchant}__campaign_{id}.txt     # WhatsApp reports")
    print("    ├── campaign_metrics_{title}.html     # HTML dashboards")
    print("    └── campaign_metrics_{title}.pdf      # PDF reports")
    
    print("\n🧪 **TESTING THE ARCHITECTURE**")
    
    # Test 1: Configuration Layer
    print("\n1️⃣  Testing Configuration Layer...")
    try:
        from campaign_reporting.config.settings import DATE_FORMAT, create_retry_options
        print(f"   ✅ Date format: {DATE_FORMAT}")
        print(f"   ✅ Retry options: Available")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Data Layer
    print("\n2️⃣  Testing Data Layer...")
    try:
        from campaign_reporting.data.file_service import ensure_merchant_directory, get_merchant_output_path
        test_merchant = "demo_merchant_123"
        merchant_dir = ensure_merchant_directory(test_merchant)
        file_path = get_merchant_output_path(test_merchant, "demo_file.txt")
        print(f"   ✅ Merchant directory: {merchant_dir}")
        print(f"   ✅ File path generation: Working")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Business Layer
    print("\n3️⃣  Testing Business Layer...")
    try:
        from campaign_reporting.business.report_generator import generate_whatsapp_performance_report
        
        # Sample campaign data
        sample_data = {
            'name': 'Demo Campaign',
            'last_updated': '2025-01-01T10:00:00',
            'date_created': '2025-01-01T09:00:00',
            'acos_target': 15.0,
            'budget': 100.0,
            'metrics': {
                'cost': 50.0,
                'total_amount': 200.0,
                'organic_units_amount': 100.0,
                'acos': 25.0,
                'advertising_items_quantity': 5,
                'organic_items_quantity': 3,
                'cvr': 2.5,
                'clicks': 100
            }
        }
        
        report = generate_whatsapp_performance_report(sample_data, None, 'pt')
        print(f"   ✅ Report generation: {len(report)} characters")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: File Organization
    print("\n4️⃣  Testing File Organization...")
    try:
        from campaign_reporting.data.file_service import write_whatsapp_report, append_campaign_to_csv
        
        test_merchant = "demo_merchant_123"
        test_content = "Demo WhatsApp report content for architecture testing."
        
        # Write WhatsApp report
        write_whatsapp_report(test_content, test_merchant, "demo_campaign")
        
        # Write CSV data
        test_campaign_data = {
            'id': 'demo_campaign',
            'name': 'Demo Campaign',
            'metrics': {'cost': 100.0, 'total_amount': 500.0, 'acos': 20.0}
        }
        append_campaign_to_csv(test_campaign_data, test_merchant)
        
        print(f"   ✅ WhatsApp report: Written to merchant directory")
        print(f"   ✅ CSV data: Appended to merchant and global files")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n📊 **CURRENT MERCHANT DIRECTORIES**")
    output_dir = "output"
    if os.path.exists(output_dir):
        merchants = [d for d in os.listdir(output_dir) if os.path.isdir(os.path.join(output_dir, d))]
        
        for merchant in merchants:
            merchant_path = os.path.join(output_dir, merchant)
            files = os.listdir(merchant_path)
            total_size = sum(os.path.getsize(os.path.join(merchant_path, f)) for f in files)
            
            print(f"\n📂 {merchant}/")
            for file in sorted(files):
                file_path = os.path.join(merchant_path, file)
                file_size = os.path.getsize(file_path)
                
                if file.endswith('.html'):
                    icon = "📄"
                elif file.endswith('.pdf'):
                    icon = "📕"
                elif file.endswith('.txt'):
                    icon = "📱"
                elif file.endswith('.csv'):
                    icon = "📊"
                else:
                    icon = "📁"
                
                print(f"   {icon} {file} ({file_size:,} bytes)")
            
            print(f"   📈 Total: {len(files)} files, {total_size:,} bytes")
    else:
        print("   📂 No merchant directories found")
    
    print("\n🚀 **HOW TO USE**")
    print("1. Run the new layered system:")
    print("   python campaign_reporting_main.py")
    print("")
    print("2. Run the original system (still works):")
    print("   python campaign_whatsapp_report.py")
    print("")
    print("3. Test the architecture:")
    print("   python test_layered_architecture.py")
    
    print("\n✨ **BENEFITS ACHIEVED**")
    print("✅ Separation of Concerns - Each layer has a single responsibility")
    print("✅ Improved Testability - Each function can be tested independently")
    print("✅ Better Maintainability - Changes are isolated to specific layers")
    print("✅ Enhanced Readability - Smaller, focused files with clear purposes")
    print("✅ Merchant Organization - Files organized by merchant for easy access")
    print("✅ Backward Compatibility - Original system still functional")
    
    print(f"\n🎉 **LAYERED ARCHITECTURE DEMO COMPLETED!**")
    print("The campaign reporting system has been successfully refactored!")

if __name__ == "__main__":
    demo_layered_architecture()
